package provider

import (
	"context"

	"demo/core/bootstrap"
)

func NewCoreServiceProvider() *CoreServiceProvider {
	return &CoreServiceProvider{}
}

type CoreServiceProvider struct{}

func (s *CoreServiceProvider) Register(ctx context.Context, container bootstrap.Container) error {
	return nil
}

func (s *CoreServiceProvider) Boot(ctx context.Context, container bootstrap.Container) error {
	return nil
}

func (s *CoreServiceProvider) Teardown(ctx context.Context, container bootstrap.Container) error {
	return nil
}
